<?php
session_start();
include "inc/navbar.php"
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Peraturan & SOP - UKM Panahan Gendewa Geni</title>
    <meta name="description" content="Standar Operasional Prosedur dan Peraturan UKM Panahan Gendewa Geni Universitas Semarang">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/animate.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #005a99;
            --secondary-color: #ff8800;
            --dark-color: #1a1a1a;
            --light-color: #f8f9fa;
            --text-color: #333;
            --border-radius: 20px;
            --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-medium: 0 8px 30px rgba(0,0,0,0.12);
            --shadow-heavy: 0 15px 50px rgba(0,0,0,0.15);
        }

        body {
            background: #ffffff;
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 100px 0 80px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,90,153,0.95), rgba(255,136,0,0.9));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-family: 'Merriweather', serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 30px;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .hero-badge {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 50px;
            padding: 12px 30px;
            display: inline-block;
            margin-bottom: 30px;
            letter-spacing: 1px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        /* Main Content */
        .main-content {
            padding: 80px 0;
            position: relative;
            background: #ffffff;
        }

        .content-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            margin: 0 auto 40px;
            max-width: 900px;
            overflow: hidden;
            position: relative;
            animation: fadeInUp 1s ease-out;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .card-title {
            font-family: 'Merriweather', serif;
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .card-body {
            padding: 40px;
        }

        /* Section Styling */
        .section-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-left: 5px solid var(--secondary-color);
            padding: 20px 25px;
            margin: 30px 0 20px;
            border-radius: 0 15px 15px 0;
            position: relative;
        }

        .section-title {
            color: var(--primary-color);
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            background: var(--secondary-color);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        /* List Styling */
        .custom-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .custom-list > li {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .custom-list > li:hover {
            transform: translateX(5px);
            box-shadow: var(--shadow-medium);
            border-left-color: var(--secondary-color);
        }

        .custom-list > li::before {
            content: counter(list-counter);
            counter-increment: list-counter;
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            position: absolute;
            left: -15px;
            top: 20px;
        }

        .custom-list {
            counter-reset: list-counter;
        }

        .sub-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 20px;
        }

        .sub-list li {
            background: white;
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-left: 3px solid var(--secondary-color);
            font-size: 0.95rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .content-card {
                margin: 0 15px 30px;
                border-radius: 15px;
            }

            .card-body {
                padding: 25px 20px;
            }

            .section-header {
                padding: 15px 20px;
                margin: 20px 0 15px;
            }

            .section-title {
                font-size: 1.2rem;
            }

            .custom-list > li {
                padding: 15px;
                margin-left: 10px;
            }
        }

        @media (max-width: 576px) {
            .hero-section {
                padding: 80px 0 60px;
            }

            .hero-title {
                font-size: 2rem;
            }

            .main-content {
                padding: 40px 0;
            }

            .card-header {
                padding: 20px 15px;
            }

            .card-title {
                font-size: 1.5rem;
            }
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Scroll animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="container">
                <div class="hero-badge">
                    <i class="fa fa-shield mr-2"></i>SOP & PERATURAN RESMI
                </div>
                <h1 class="hero-title">Standar Operasional Prosedur</h1>
                <p class="hero-subtitle">UKM Panahan Gendewa Geni Universitas Semarang</p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content">
        <div class="container">
            <!-- BAB I - Maksud -->
            <div class="content-card animate-on-scroll">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fa fa-bullseye mr-3"></i>BAB I - Maksud
                    </h2>
                </div>
                <div class="card-body">
                    <div class="section-header">
                        <h3 class="section-title">
                            <span class="section-icon">
                                <i class="fa fa-flag"></i>
                            </span>
                            Tujuan Pedoman
                        </h3>
                    </div>
                    <ul class="custom-list">
                        <li>
                            <strong>Pedoman Perilaku:</strong> Memberikan pedoman dan tata tertib bagi kepengurusan dan keanggotaan UKM Panahan untuk berperilaku yang normal dan baik dalam melaksanakan aktivitas di UKM, kampus, maupun lingkungan masyarakat yang berhubungan dengan UKM Panahan dan Universitas Semarang.
                        </li>
                    </ul>
                </div>
            </div>

            <!-- BAB II - Tujuan -->
            <div class="content-card animate-on-scroll">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fa fa-target mr-3"></i>BAB II - Tujuan
                    </h2>
                </div>
                <div class="card-body">
                    <div class="section-header">
                        <h3 class="section-title">
                            <span class="section-icon">
                                <i class="fa fa-star"></i>
                            </span>
                            Tujuan Utama
                        </h3>
                    </div>
                    <ul class="custom-list">
                        <li>
                            <strong>Komitmen Anggota:</strong> Mewujudkan komitmen anggota ketika bersedia untuk terlibat menjadi kepengurusan.
                        </li>
                        <li>
                            <strong>Pembentukan Karakter:</strong> Membentuk keanggotaan yang beretika normal dan baik di lingkungan manapun yang berhubungan dengan UKM Panahan dan Universitas Semarang.
                        </li>
                        <li>
                            <strong>Perlindungan Anggota:</strong> UKM akan melindungi setiap anggota yang mendapat hal-hal yang membahayakan dari pihak manapun.
                        </li>
                        <li>
                            <strong>Kenyamanan & Keamanan:</strong> UKM memberikan kenyamanan dan keamanan bagi setiap anggota.
                        </li>
                    </ul>
                </div>
            </div>
            <!-- BAB III - SOP UKM Panahan -->
            <div class="content-card animate-on-scroll">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fa fa-list-alt mr-3"></i>BAB III - SOP UKM Panahan
                    </h2>
                </div>
                <div class="card-body">
                    <div class="section-header">
                        <h3 class="section-title">
                            <span class="section-icon">
                                <i class="fa fa-check-circle"></i>
                            </span>
                            Kewajiban & Tanggung Jawab
                        </h3>
                    </div>
                    <ul class="custom-list">
                        <li>
                            <strong>Menjaga Nama Baik:</strong> Menjaga nama baik UKM dan kampus dalam setiap aktivitas.
                        </li>
                        <li>
                            <strong>Saling Menghargai:</strong> Menghargai sesama anggota dan kepengurusan UKM.
                        </li>
                        <li>
                            <strong>Moralitas Tinggi:</strong> Memiliki moralitas yang tinggi dalam berperilaku.
                        </li>
                        <li>
                            <strong>Profesionalisme:</strong> Profesional atas pekerjaan sesuai divisi masing-masing.
                        </li>
                        <li>
                            <strong>Ketaatan Peraturan:</strong> Menaati peraturan SOP UKM Panahan dan Universitas Semarang.
                        </li>
                        <li>
                            <strong>Menjaga Keharmonisan:</strong> Tidak menimbulkan perselisihan apa pun antar anggota UKM.
                        </li>
                        <li>
                            <strong>Tanggung Jawab:</strong> Bertanggung jawab atas segala perbuatan kesalahan dan menerima hukuman sesuai keputusan dari Ketua/Wakil UKM Panahan.
                        </li>
                        <li>
                            <strong>Menjaga Reputasi:</strong> Tidak melakukan perbuatan yang merugikan nama baik UKM Panahan dan Universitas Semarang.
                        </li>
                        <li>
                            <strong>Anti Pelecehan:</strong> Tidak melakukan tindak pelecehan seksual secara apapun (Verbal/Non Verbal).
                        </li>
                        <li>
                            <strong>Konsekuensi Pelanggaran:</strong> Segala pelanggaran yang dilakukan, akan berakibat sebagai berikut:
                            <ul class="sub-list">
                                <li>
                                    <strong>Surat Peringatan (SP):</strong> Dikenakan SP yang berakibat pengeluaran/pencopotan divisi jika mendapat 3 kali SP.
                                </li>
                                <li>
                                    <strong>Denda:</strong> Dikenakan denda terhadap pelaku sesuai jenis pelanggaran yang dilakukan. Contoh: pelecehan, kekerasan, pengancaman, pembullyan (verbal/non verbal).
                                </li>
                                <li>
                                    <strong>Proses Persidangan:</strong> Segala keputusan hukuman atas pelanggaran akan dilaksanakan oleh Ketua/Wakil bersama Humas dan Konsolidator menggunakan metode persidangan dengan pelaku dan korban yang bersangkutan.
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Contact & Info Section -->
            <div class="content-card animate-on-scroll">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fa fa-info-circle mr-3"></i>Informasi Tambahan
                    </h2>
                </div>
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <i class="fa fa-calendar fa-2x text-primary mb-2"></i>
                                <h5>Berlaku Sejak</h5>
                                <p class="text-muted">Tahun Akademik 2024/2025</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <i class="fa fa-users fa-2x text-success mb-2"></i>
                                <h5>Untuk Semua</h5>
                                <p class="text-muted">Anggota & Kepengurusan</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <i class="fa fa-shield fa-2x text-warning mb-2"></i>
                                <h5>Perlindungan</h5>
                                <p class="text-muted">Hak & Kewajiban Terjamin</p>
                            </div>
                        </div>
                    </div>
                    <hr class="my-4">
                    <p class="text-muted mb-0">
                        <i class="fa fa-quote-left mr-2"></i>
                        Dengan mematuhi SOP ini, kita bersama-sama membangun UKM Panahan yang profesional, harmonis, dan berprestasi.
                        <i class="fa fa-quote-right ml-2"></i>
                    </p>
                </div>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer>
        <?php include "inc/footer.php"; ?>
    </footer>

    <!-- Scripts -->
    <script src="js/vendor/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.counterup.min.js"></script>
    <script src="js/waypoints.min.js"></script>
    <script src="js/animate.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize dropdown
            $('.dropdown-toggle').dropdown();

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Scroll animations
            function animateOnScroll() {
                $('.animate-on-scroll').each(function() {
                    var elementTop = $(this).offset().top;
                    var elementBottom = elementTop + $(this).outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();

                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).addClass('animated');
                    }
                });
            }

            // Initial check
            animateOnScroll();

            // Check on scroll
            $(window).on('scroll', function() {
                animateOnScroll();
            });

            // Add hover effects to list items
            $('.custom-list > li').hover(
                function() {
                    $(this).find('.section-icon').addClass('fa-spin');
                },
                function() {
                    $(this).find('.section-icon').removeClass('fa-spin');
                }
            );

            // Add click effect to cards
            $('.content-card').on('click', function() {
                $(this).addClass('pulse');
                setTimeout(() => {
                    $(this).removeClass('pulse');
                }, 600);
            });
        });

        // Add pulse animation class
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .pulse {
                    animation: pulse 0.6s ease-in-out;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.02); }
                    100% { transform: scale(1); }
                }
            `)
            .appendTo('head');
    </script>
</body>
</html>